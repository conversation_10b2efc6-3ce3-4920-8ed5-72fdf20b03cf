# coding:utf-8
# enhanced_wx_crawler.py - 增强版微信文章链接抓取器
import os
import requests
import json
import urllib3
import utils
import pandas as pd
from datetime import datetime
import time


class EnhancedWxCrawler(object):
    """增强版翻页内容抓取，支持保存到文件"""
    urllib3.disable_warnings()

    def __init__(self, appmsg_token, biz, cookie, begin_page_index=0, end_page_index=5, save_to_file=True):
        # 起始页数
        self.begin_page_index = begin_page_index
        # 结束页数
        self.end_page_index = end_page_index
        # 抓了多少条了
        self.num = 1
        # 是否保存到文件
        self.save_to_file = save_to_file
        # 存储抓取的文章数据
        self.articles_data = []

        self.appmsg_token = appmsg_token
        self.biz = biz
        self.headers = {
            "User-Agent": "Mozilla/5.0 AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/57.0.2987.132 MQQBrowser/6.2 Mobile",
            "Cookie": cookie
        }
        self.cookie = cookie

    def article_list(self, context):
        """解析文章列表"""
        try:
            articles = json.loads(context).get('general_msg_list')
            return json.loads(articles)
        except Exception as e:
            print(f"❌ 解析文章列表失败: {e}")
            return None

    def extract_articles_from_page(self, articles_data):
        """从页面数据中提取文章信息"""
        extracted_articles = []
        
        if not articles_data or 'list' not in articles_data:
            return extracted_articles
        
        for a in articles_data['list']:
            # 公众号中主条
            if 'app_msg_ext_info' in a.keys() and '' != a.get('app_msg_ext_info').get('content_url',''):
                article_info = {
                    'title': a.get('app_msg_ext_info').get('title', ''),
                    'url': a.get('app_msg_ext_info').get('content_url', ''),
                    'pub_time': self.format_time(a.get('comm_msg_info', {}).get('datetime', 0)),
                    'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                extracted_articles.append(article_info)
                print(f"{self.num}条 [主] {article_info['title']}")
                self.num += 1
                
            # 公众号中副条
            if 'app_msg_ext_info' in a.keys():
                for m in a.get('app_msg_ext_info').get('multi_app_msg_item_list',[]):
                    article_info = {
                        'title': m.get('title', ''),
                        'url': m.get('content_url', ''),
                        'pub_time': self.format_time(a.get('comm_msg_info', {}).get('datetime', 0)),
                        'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    extracted_articles.append(article_info)
                    print(f"{self.num}条 [副] {article_info['title']}")
                    self.num += 1
        
        return extracted_articles

    def format_time(self, timestamp):
        """格式化时间戳"""
        try:
            if timestamp:
                return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
            else:
                return ''
        except:
            return ''

    def save_data(self):
        """保存数据到文件"""
        if not self.articles_data:
            print("⚠️ 没有数据需要保存")
            return None, None
        
        # 创建数据目录
        data_dir = "./data/basic_links"
        os.makedirs(data_dir, exist_ok=True)
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        excel_file = os.path.join(data_dir, f"article_links_{timestamp}.xlsx")
        json_file = os.path.join(data_dir, f"article_links_{timestamp}.json")
        
        try:
            # 保存为Excel
            df = pd.DataFrame(self.articles_data)
            df.to_excel(excel_file, index=False, engine='openpyxl')
            print(f"📊 Excel文件已保存: {excel_file}")
            
            # 保存为JSON
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(self.articles_data, f, ensure_ascii=False, indent=2)
            print(f"💾 JSON文件已保存: {json_file}")
            
            return excel_file, json_file
            
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
            return None, None

    def run(self):
        """运行爬虫"""
        current_page = self.begin_page_index
        
        print(f"🚀 开始抓取文章链接...")
        print(f"📋 页数范围: {self.begin_page_index} - {self.end_page_index}")
        
        while current_page <= self.end_page_index:
            try:
                print(f"\n📄 正在抓取第 {current_page + 1} 页...")
                
                # 翻页地址
                page_url = "https://mp.weixin.qq.com/mp/profile_ext?action=getmsg&__biz={}&f=json&offset={}&count=10&is_ok=1&scene=&uin=777&key=777&pass_ticket={}&wxtoken=&appmsg_token={}&x5=0&f=json"
                
                # 将 cookie 字符串清理并字典化
                clean_cookie = self.cookie.replace('\u00a0', ' ').strip()
                wx_dict = utils.str_to_dict(clean_cookie, join_symbol='; ', split_symbol='=')
                
                # 请求地址
                response = requests.get(
                    page_url.format(
                        self.biz, 
                        current_page * 10, 
                        wx_dict['pass_ticket'], 
                        self.appmsg_token
                    ), 
                    headers=self.headers, 
                    verify=False,
                    timeout=30
                )
                
                if response.status_code != 200:
                    print(f"❌ 请求失败，状态码: {response.status_code}")
                    break
                
                # 将文章列表字典化
                articles = self.article_list(response.text)
                
                if not articles:
                    print("❌ 解析文章列表失败，可能Cookie已过期")
                    break
                
                # 提取文章信息
                page_articles = self.extract_articles_from_page(articles)
                
                if not page_articles:
                    print("⚠️ 本页没有找到文章，可能已到最后一页")
                    break
                
                # 添加到总数据中
                self.articles_data.extend(page_articles)
                print(f"✅ 第 {current_page + 1} 页完成，获取 {len(page_articles)} 篇文章")
                
                current_page += 1
                
                # 添加延迟避免被封
                if current_page <= self.end_page_index:
                    print("⏳ 等待 3 秒...")
                    time.sleep(3)
                
            except KeyboardInterrupt:
                print("\n⏹️ 用户中断抓取")
                break
            except Exception as e:
                print(f"❌ 抓取第 {current_page + 1} 页时出错: {e}")
                current_page += 1
                continue
        
        # 保存数据
        if self.save_to_file and self.articles_data:
            self.save_data()
        
        print(f"\n🎉 抓取完成！")
        print(f"📊 总共获取 {len(self.articles_data)} 篇文章链接")
        
        return self.articles_data

    def print_summary(self):
        """打印抓取摘要"""
        if not self.articles_data:
            print("📊 没有数据可显示")
            return
        
        print(f"\n📊 抓取摘要")
        print("=" * 50)
        print(f"📖 总文章数: {len(self.articles_data)}")
        
        # 统计主条和副条
        main_count = len([a for a in self.articles_data if a['type'] == '主条'])
        sub_count = len([a for a in self.articles_data if a['type'] == '副条'])
        print(f"📰 主条文章: {main_count}")
        print(f"📄 副条文章: {sub_count}")
        
        # 显示最新几篇文章
        print(f"\n📋 最新文章:")
        for i, article in enumerate(self.articles_data[:5]):
            print(f"   {i+1}. {article['title']}")
        
        if len(self.articles_data) > 5:
            print(f"   ... 还有 {len(self.articles_data) - 5} 篇文章")
