# coding:utf-8
# main_with_content.py - 获取文章内容的示例脚本

from enhanced_wx_crawler import EnhancedWxCrawler
import json

def main():
    """主函数 - 演示如何获取文章内容"""
    
    print("🔧 微信文章内容抓取器")
    print("=" * 50)
    
    # 从文件读取配置（如果存在）
    try:
        with open('wechat_keys.txt', 'r', encoding='utf-8') as f:
            lines = f.readlines()
            if len(lines) >= 3:
                appmsg_token = lines[0].strip()
                biz = lines[1].strip()
                cookie = lines[2].strip()
                print("✅ 从配置文件读取参数成功")
            else:
                raise FileNotFoundError
    except FileNotFoundError:
        print("⚠️ 未找到配置文件，请手动输入参数")
        print("\n请输入以下参数（从微信公众号后台获取）:")
        appmsg_token = input("appmsg_token: ").strip()
        biz = input("biz: ").strip()
        cookie = input("cookie: ").strip()
    
    # 获取抓取参数
    print("\n📋 抓取参数设置:")
    try:
        begin_page = int(input("起始页数 (默认0): ") or "0")
        end_page = int(input("结束页数 (默认2): ") or "2")
        
        # 询问是否获取文章内容
        get_content_input = input("是否获取文章内容? (y/n, 默认n): ").strip().lower()
        get_content = get_content_input in ['y', 'yes', '是', '1']
        
        if get_content:
            print("⚠️ 获取文章内容会显著增加抓取时间，请耐心等待")
            confirm = input("确认继续? (y/n): ").strip().lower()
            if confirm not in ['y', 'yes', '是', '1']:
                print("❌ 用户取消操作")
                return
        
    except ValueError:
        print("❌ 输入格式错误，使用默认值")
        begin_page = 0
        end_page = 2
        get_content = False
    
    # 创建爬虫实例
    crawler = EnhancedWxCrawler(
        appmsg_token=appmsg_token,
        biz=biz,
        cookie=cookie,
        begin_page_index=begin_page,
        end_page_index=end_page,
        save_to_file=True,
        get_content=get_content  # 新增参数：是否获取文章内容
    )
    
    try:
        # 开始抓取
        articles = crawler.run()
        
        # 显示摘要
        crawler.print_summary()
        
        if articles:
            print(f"\n🎉 抓取完成！")
            if get_content:
                print(f"📄 获取了 {len([a for a in articles if a.get('content')])} 篇文章的完整内容")
            else:
                print(f"🔗 获取了 {len(articles)} 篇文章的链接信息")
            
            # 显示示例数据
            if articles and get_content:
                print(f"\n📖 内容示例（前100字符）:")
                for i, article in enumerate(articles[:3]):
                    if article.get('content'):
                        content_preview = article['content'][:100] + "..." if len(article['content']) > 100 else article['content']
                        print(f"   {i+1}. {article['title']}")
                        print(f"      内容: {content_preview}")
                        print(f"      长度: {article.get('content_length', 0)} 字符")
                        print()
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断抓取")
    except Exception as e:
        print(f"❌ 抓取过程中出错: {e}")

def demo_content_only():
    """演示仅获取内容的功能"""
    print("\n🔧 单篇文章内容获取演示")
    print("=" * 30)
    
    # 示例：直接获取单篇文章内容
    test_url = input("请输入文章URL: ").strip()
    if not test_url:
        print("❌ 未输入URL")
        return
    
    # 需要cookie来访问文章
    cookie = input("请输入Cookie: ").strip()
    if not cookie:
        print("❌ 未输入Cookie")
        return
    
    # 创建临时爬虫实例
    crawler = EnhancedWxCrawler("", "", cookie)
    
    # 获取文章内容
    content_data = crawler.get_article_content(test_url)
    
    if content_data:
        if content_data.get('error'):
            print(f"❌ 获取失败: {content_data['error']}")
        else:
            print(f"✅ 获取成功!")
            print(f"标题: {content_data.get('title', '未知')}")
            print(f"作者: {content_data.get('author', '未知')}")
            print(f"发布时间: {content_data.get('pub_time', '未知')}")
            print(f"内容长度: {content_data.get('content_length', 0)} 字符")
            print(f"内容预览: {content_data.get('content', '')[:200]}...")
    else:
        print("❌ 获取文章内容失败")

if __name__ == "__main__":
    print("选择功能:")
    print("1. 批量抓取文章（可选择是否获取内容）")
    print("2. 单篇文章内容获取演示")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == "2":
        demo_content_only()
    else:
        main()
