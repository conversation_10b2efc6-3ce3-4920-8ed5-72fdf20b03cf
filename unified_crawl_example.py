# coding:utf-8
# unified_crawl_example.py - 统一爬取示例

"""
微信公众号文章统一爬取示例
不再区分主条和副条，统一处理所有文章
"""

from enhanced_wx_crawler import EnhancedWxCrawler

def main():
    print("🚀 微信公众号文章统一爬取示例")
    print("=" * 50)
    
    # 配置参数（请替换为您的真实参数）
    config = {
        'appmsg_token': 'your_appmsg_token_here',
        'biz': 'your_biz_here', 
        'cookie': 'your_cookie_here',
        'begin_page_index': 0,
        'end_page_index': 2,  # 建议先测试少量页面
        'save_to_file': True,
        'get_content': True   # 获取完整文章内容
    }
    
    print("📋 配置信息:")
    print(f"   页面范围: {config['begin_page_index']} - {config['end_page_index']}")
    print(f"   获取内容: {'是' if config['get_content'] else '否'}")
    print(f"   保存文件: {'是' if config['save_to_file'] else '否'}")
    print()
    
    try:
        # 创建爬虫实例
        crawler = EnhancedWxCrawler(**config)
        
        print("🔍 开始爬取...")
        print("📝 注意：现在所有文章（主条+副条）都会统一处理")
        print()
        
        # 开始爬取
        articles = crawler.run()
        
        print("\n" + "=" * 50)
        print("📊 爬取完成！")
        print(f"总共获取文章: {len(articles)} 篇")
        
        if articles:
            print("\n📄 文章列表预览:")
            for i, article in enumerate(articles[:5], 1):  # 只显示前5篇
                print(f"{i}. {article['title']}")
                if config['get_content'] and 'content_length' in article:
                    print(f"   内容长度: {article['content_length']} 字符")
                print(f"   发布时间: {article['pub_time']}")
                print()
            
            if len(articles) > 5:
                print(f"... 还有 {len(articles) - 5} 篇文章")
        
        print("✅ 所有文章已保存到文件中")
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 爬取失败: {e}")
        print("请检查配置参数是否正确")

if __name__ == "__main__":
    print("⚠️ 使用前请先配置正确的参数:")
    print("   1. appmsg_token")
    print("   2. biz") 
    print("   3. cookie")
    print()
    
    choice = input("确认已配置参数？(y/n): ").lower()
    if choice == 'y':
        main()
    else:
        print("请先配置参数后再运行")
