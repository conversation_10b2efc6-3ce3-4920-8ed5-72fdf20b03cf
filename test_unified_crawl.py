# coding:utf-8
# test_unified_crawl.py - 测试统一爬取功能

print("🧪 测试统一爬取功能...")

try:
    from enhanced_wx_crawler import EnhancedWxCrawler
    print("✅ 导入成功")
    
    # 测试创建实例
    crawler = EnhancedWxCrawler(
        appmsg_token="test_token",
        biz="test_biz", 
        cookie="test_cookie",
        get_content=True
    )
    print(f"✅ 创建实例成功，get_content = {crawler.get_content}")
    
    # 模拟测试数据
    test_data = {
        'list': [
            {
                'app_msg_ext_info': {
                    'title': '测试主条文章',
                    'content_url': 'https://mp.weixin.qq.com/s/test1',
                    'multi_app_msg_item_list': [
                        {
                            'title': '测试副条文章1',
                            'content_url': 'https://mp.weixin.qq.com/s/test2'
                        },
                        {
                            'title': '测试副条文章2', 
                            'content_url': 'https://mp.weixin.qq.com/s/test3'
                        }
                    ]
                },
                'comm_msg_info': {
                    'datetime': 1640995200  # 2022-01-01 00:00:00
                }
            }
        ]
    }
    
    print("\n🔍 测试文章提取逻辑...")
    
    # 临时禁用内容获取以避免网络请求
    crawler.get_content = False
    articles = crawler.extract_articles_from_page(test_data)
    
    print(f"\n📊 提取结果:")
    print(f"总共提取文章数: {len(articles)}")
    
    for i, article in enumerate(articles, 1):
        print(f"{i}. {article['title']}")
        print(f"   URL: {article['url']}")
        print(f"   发布时间: {article['pub_time']}")
    
    print("\n🎉 测试完成！")
    print("✅ 现在所有文章（主条+副条）都会统一处理，不再区分类型")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
