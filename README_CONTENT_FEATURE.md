# 微信文章内容获取功能说明

## 🆕 新增功能

在原有的文章链接抓取功能基础上，新增了**文章内容获取**功能，可以获取文章的完整正文内容。

## 📋 功能特性

### 1. 文章内容获取
- ✅ 获取文章标题
- ✅ 获取文章作者
- ✅ 获取文章正文内容（纯文本）
- ✅ 获取发布时间
- ✅ 统计内容字符数
- ✅ 自动清理HTML标签和脚本
- ✅ 验证码检测和处理

### 2. 灵活的使用方式
- 🔗 **仅获取链接**：快速抓取文章链接信息（原功能）
- 📄 **获取完整内容**：抓取链接 + 完整文章内容（新功能）
- 🎯 **单篇文章内容获取**：针对特定文章URL获取内容

### 3. 智能保存
- 📁 **分类保存**：
  - 仅链接：`./data/basic_links/`
  - 含内容：`./data/with_content/`
- 📊 **多格式支持**：Excel (.xlsx) + JSON (.json)
- 🏷️ **文件命名**：自动添加时间戳

## 🚀 使用方法

### 方法一：使用新的主程序

```bash
python main_with_content.py
```

程序会提供两个选项：
1. **批量抓取文章**（可选择是否获取内容）
2. **单篇文章内容获取演示**

### 方法二：直接使用增强版爬虫类

#### 仅获取文章链接（快速）
```python
from enhanced_wx_crawler import EnhancedWxCrawler

crawler = EnhancedWxCrawler(
    appmsg_token="your_token",
    biz="your_biz", 
    cookie="your_cookie",
    begin_page_index=0,
    end_page_index=5,
    save_to_file=True,
    get_content=False  # 不获取内容
)
articles = crawler.run()
```

#### 获取文章链接和完整内容（慢速）
```python
from enhanced_wx_crawler import EnhancedWxCrawler

crawler = EnhancedWxCrawler(
    appmsg_token="your_token",
    biz="your_biz",
    cookie="your_cookie", 
    begin_page_index=0,
    end_page_index=2,  # 建议减少页数
    save_to_file=True,
    get_content=True   # 获取完整内容
)
articles = crawler.run()
```

#### 单独获取某篇文章内容
```python
from enhanced_wx_crawler import EnhancedWxCrawler

crawler = EnhancedWxCrawler("", "", "your_cookie")
content = crawler.get_article_content("article_url")

if content and not content.get('error'):
    print(f"标题: {content['title']}")
    print(f"作者: {content['author']}")
    print(f"内容长度: {content['content_length']} 字符")
    print(f"内容预览: {content['content'][:100]}...")
```

## 📊 数据结构

### 仅链接模式的数据结构
```json
{
    "title": "文章标题",
    "url": "文章链接",
    "pub_time": "2024-01-01 12:00:00",
    "crawl_time": "2024-01-01 12:00:00"
}
```

### 含内容模式的数据结构
```json
{
    "title": "文章标题",
    "url": "文章链接", 
    "pub_time": "2024-01-01 12:00:00",
    "crawl_time": "2024-01-01 12:00:00",
    "content": "文章完整内容...",
    "content_length": 1500,
    "author": "作者名称",
    "detailed_pub_time": "详细发布时间"
}
```

## ⚠️ 重要注意事项

### 1. 速度差异
- **仅链接模式**：每页约3-5秒，速度较快
- **含内容模式**：每篇文章约2-3秒，速度较慢

### 2. 建议设置
- 获取内容时建议**减少页数范围**（如0-2页）
- 程序会自动添加延迟避免被封
- 遇到验证码时会自动停止

### 3. 错误处理
- ✅ 验证码检测：自动停止并提示
- ✅ 非文章页面：跳过并继续
- ✅ 网络错误：重试机制
- ✅ Cookie过期：及时提示

## 🔧 依赖要求

确保已安装以下依赖：
```bash
pip install beautifulsoup4 requests pandas openpyxl
```

或使用项目的requirements.txt：
```bash
pip install -r requirements.txt
```

## 📁 文件说明

- `enhanced_wx_crawler.py` - 增强版爬虫类（新增内容获取功能）
- `main_with_content.py` - 新的主程序（支持内容获取）
- `test_content_feature.py` - 功能测试脚本
- `simple_test.py` - 简单测试脚本

## 🎯 使用场景

1. **内容分析**：获取完整文章内容进行文本分析
2. **数据备份**：备份重要文章的完整内容
3. **内容监控**：监控特定公众号的内容更新
4. **研究用途**：学术研究或市场分析

## 💡 性能优化建议

1. **批量获取内容时**：
   - 减少页数范围（建议不超过3页）
   - 在网络良好的环境下运行
   - 避免在高峰时段运行

2. **单篇文章获取**：
   - 适合获取特定重要文章
   - 可以随时中断和重新开始

3. **存储优化**：
   - 定期清理旧的数据文件
   - 根据需要选择JSON或Excel格式

## 🔍 故障排除

1. **导入错误**：确保安装了beautifulsoup4
2. **验证码问题**：降低抓取频率，手动完成验证
3. **内容获取失败**：检查Cookie是否有效
4. **保存失败**：检查磁盘空间和写入权限

---

**提示**：首次使用建议先测试仅链接模式，确认功能正常后再尝试内容获取模式。
