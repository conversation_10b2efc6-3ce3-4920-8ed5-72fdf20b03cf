# coding:utf-8
# test_content_feature.py - 测试文章内容获取功能

from enhanced_wx_crawler import EnhancedWxCrawler
import json

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本功能...")
    
    # 测试参数
    test_appmsg_token = "test_token"
    test_biz = "test_biz"
    test_cookie = "test_cookie"
    
    # 测试不获取内容的情况
    print("\n1. 测试不获取内容的爬虫...")
    crawler1 = EnhancedWxCrawler(
        appmsg_token=test_appmsg_token,
        biz=test_biz,
        cookie=test_cookie,
        begin_page_index=0,
        end_page_index=1,
        save_to_file=False,
        get_content=False
    )
    print(f"✅ 创建成功，get_content = {crawler1.get_content}")
    
    # 测试获取内容的情况
    print("\n2. 测试获取内容的爬虫...")
    crawler2 = EnhancedWxCrawler(
        appmsg_token=test_appmsg_token,
        biz=test_biz,
        cookie=test_cookie,
        begin_page_index=0,
        end_page_index=1,
        save_to_file=False,
        get_content=True
    )
    print(f"✅ 创建成功，get_content = {crawler2.get_content}")
    
    print("\n✅ 基本功能测试通过")

def test_content_extraction():
    """测试内容提取功能（使用模拟HTML）"""
    print("\n🧪 测试内容提取功能...")
    
    # 创建测试用的HTML内容
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta property="og:title" content="测试文章标题">
        <meta property="og:article:author" content="测试作者">
    </head>
    <body>
        <h1 class="rich_media_title">测试文章标题</h1>
        <a class="rich_media_meta_link">测试作者</a>
        <em class="rich_media_meta_text">2024-01-01</em>
        <div class="rich_media_content" id="js_content">
            <p>这是测试文章的内容。</p>
            <p>包含多个段落。</p>
            <script>console.log('这个脚本应该被移除');</script>
            <style>.test { color: red; }</style>
            <p>最后一个段落。</p>
        </div>
    </body>
    </html>
    """
    
    # 测试BeautifulSoup解析
    try:
        from bs4 import BeautifulSoup
        import re
        
        soup = BeautifulSoup(test_html, 'html.parser')
        
        # 提取标题
        title_tag = soup.find('h1', {'class': 'rich_media_title'})
        title = title_tag.get_text(strip=True) if title_tag else ""
        print(f"标题: {title}")
        
        # 提取作者
        author_tag = soup.find('a', {'class': 'rich_media_meta_link'})
        author = author_tag.get_text(strip=True) if author_tag else ""
        print(f"作者: {author}")
        
        # 提取内容
        content_div = soup.find('div', {'class': 'rich_media_content'})
        if content_div:
            # 移除脚本和样式标签
            for script in content_div(["script", "style"]):
                script.decompose()
            
            content = content_div.get_text(separator='\n', strip=True)
            content = re.sub(r'\n\s*\n', '\n\n', content)
            content = content.strip()
            print(f"内容: {content}")
            print(f"内容长度: {len(content)} 字符")
        
        print("✅ 内容提取测试通过")
        
    except ImportError:
        print("❌ BeautifulSoup4 未安装，请运行: pip install beautifulsoup4")
    except Exception as e:
        print(f"❌ 内容提取测试失败: {e}")

def show_usage_examples():
    """显示使用示例"""
    print("\n📖 使用示例:")
    print("=" * 50)
    
    print("\n1. 仅获取文章链接（快速）:")
    print("""
crawler = EnhancedWxCrawler(
    appmsg_token="your_token",
    biz="your_biz", 
    cookie="your_cookie",
    begin_page_index=0,
    end_page_index=5,
    save_to_file=True,
    get_content=False  # 不获取内容
)
articles = crawler.run()
    """)
    
    print("\n2. 获取文章链接和完整内容（慢速）:")
    print("""
crawler = EnhancedWxCrawler(
    appmsg_token="your_token",
    biz="your_biz",
    cookie="your_cookie", 
    begin_page_index=0,
    end_page_index=2,  # 建议减少页数
    save_to_file=True,
    get_content=True   # 获取完整内容
)
articles = crawler.run()
    """)
    
    print("\n3. 单独获取某篇文章内容:")
    print("""
crawler = EnhancedWxCrawler("", "", "your_cookie")
content = crawler.get_article_content("article_url")
if content and not content.get('error'):
    print(f"标题: {content['title']}")
    print(f"内容: {content['content'][:100]}...")
    """)
    
    print("\n📁 保存位置:")
    print("- 仅链接: ./data/basic_links/")
    print("- 含内容: ./data/with_content/")
    
    print("\n⚠️ 注意事项:")
    print("- 获取内容会显著增加抓取时间")
    print("- 建议获取内容时减少页数范围")
    print("- 遇到验证码时会自动停止")
    print("- 内容包含：标题、作者、正文、发布时间、字符数等")

if __name__ == "__main__":
    print("🔧 文章内容获取功能测试")
    print("=" * 40)
    
    # 运行测试
    test_basic_functionality()
    test_content_extraction()
    show_usage_examples()
    
    print(f"\n🎉 测试完成！")
    print(f"💡 运行 python main_with_content.py 开始使用")
