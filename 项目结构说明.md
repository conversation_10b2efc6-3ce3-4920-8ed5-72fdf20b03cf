# 📁 项目结构说明

## 🎯 核心文件

### 主程序
- `main_enhanced.py` - **主程序入口**，提供用户界面和功能选择

### 核心功能模块
- `batch_readnum_spider.py` - **批量阅读量抓取核心**，实现统计数据抓取
- `read_cookie.py` - **Cookie管理**，处理微信认证信息
- `wxCrawler.py` - **基础爬虫**，获取文章链接
- `enhanced_wx_crawler.py` - **增强爬虫**，带文件保存功能

### 工具模块
- `utils.py` - **工具函数**，通用功能
- `diagnose.py` - **系统诊断**，故障排除工具

### 配置文件
- `requirements.txt` - **依赖列表**，Python包依赖
- `wechat_keys.txt` - **Cookie存储**，微信认证信息
- `chromedriver.exe` - **浏览器驱动**，用于Cookie抓取

## 📊 数据目录

### data/
- `readnum_batch/` - **批量阅读量数据**，Excel和JSON格式
- `basic_links/` - **基础链接数据**，文章链接信息
- `mixed/` - **混合数据**，其他类型数据
- `南京市发展和改革委员会/` - **特定公众号数据**
- `南京市工信局/` - **特定公众号数据**
- `南京教育发布/` - **特定公众号数据**

## 🚀 使用流程

1. **运行主程序**: `python main_enhanced.py`
2. **选择功能1**: 抓取Cookie（首次使用）
3. **选择功能3**: 批量阅读量统计抓取
4. **查看结果**: 数据自动保存到data目录

## 🔧 文件依赖关系

```
main_enhanced.py
├── batch_readnum_spider.py
│   ├── read_cookie.py
│   ├── utils.py
│   └── requests, pandas, openpyxl
├── enhanced_wx_crawler.py
│   ├── wxCrawler.py
│   └── utils.py
└── diagnose.py
```

## 📋 清理说明

已移除的不相干文件：
- 测试文件：`test_*.py`
- 调试文件：`debug_*.py`
- 临时文件：`fix_*.py`
- 重复文件：`spider_*.py`
- 说明文档：多个markdown文件
- 缓存目录：`__pycache__/`
- 调试数据：`debug/` 目录下的HTML文件

保留的核心文件都是系统正常运行所必需的。
