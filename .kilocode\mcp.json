{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "D:\\wechat_spider"]}, "sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest", "--browser=", "--headless=", "--viewport-size="]}, "figma-dev-mode": {"type": "sse", "url": "http://127.0.0.1:3845/sse"}}}