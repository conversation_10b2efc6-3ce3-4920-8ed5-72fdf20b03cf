# coding:utf-8
# simple_test.py - 简单测试

print("🧪 开始测试...")

try:
    from enhanced_wx_crawler import EnhancedWxCrawler
    print("✅ 导入 EnhancedWxCrawler 成功")
    
    # 测试创建实例
    crawler = EnhancedWxCrawler(
        appmsg_token="test_token",
        biz="test_biz", 
        cookie="test_cookie",
        get_content=True
    )
    print(f"✅ 创建实例成功，get_content = {crawler.get_content}")
    
    # 测试BeautifulSoup导入
    from bs4 import BeautifulSoup
    print("✅ BeautifulSoup 导入成功")
    
    # 测试正则表达式
    import re
    print("✅ re 模块导入成功")
    
    print("🎉 所有测试通过！")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
except Exception as e:
    print(f"❌ 其他错误: {e}")
